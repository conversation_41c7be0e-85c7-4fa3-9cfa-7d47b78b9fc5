import { motion } from "framer-motion";
import { 
  Zap, 
  <PERSON>, 
  Sparkles, 
  Palette, 
  Code, 
  Smartphone,
  Globe,
  Lock,
  Rocket
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { cn } from "~/lib/utils";

interface FeaturesProps {
  className?: string;
}

export function Features({ className }: FeaturesProps) {
  const features = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Built with Vite and optimized for performance. Experience blazing fast load times and smooth interactions.",
      color: "text-yellow-500",
      bgColor: "bg-yellow-500/10",
    },
    {
      icon: Shield,
      title: "Secure by Design",
      description: "Enterprise-grade security with built-in protection against common vulnerabilities and attacks.",
      color: "text-green-500",
      bgColor: "bg-green-500/10",
    },
    {
      icon: Sparkles,
      title: "Modern UI/UX",
      description: "Beautiful, accessible interfaces with smooth animations and micro-interactions that delight users.",
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
    },
    {
      icon: Palette,
      title: "Customizable Themes",
      description: "Dark and light modes with fully customizable color schemes and design tokens.",
      color: "text-pink-500",
      bgColor: "bg-pink-500/10",
    },
    {
      icon: Code,
      title: "Developer Experience",
      description: "TypeScript, ESLint, Prettier, and hot reload for the best development experience.",
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
    },
    {
      icon: Smartphone,
      title: "Mobile First",
      description: "Responsive design that works perfectly on all devices, from mobile to desktop.",
      color: "text-cyan-500",
      bgColor: "bg-cyan-500/10",
    },
    {
      icon: Globe,
      title: "SEO Optimized",
      description: "Built-in SEO optimization with server-side rendering and meta tag management.",
      color: "text-indigo-500",
      bgColor: "bg-indigo-500/10",
    },
    {
      icon: Lock,
      title: "Type Safety",
      description: "Full TypeScript support with strict type checking for robust, maintainable code.",
      color: "text-red-500",
      bgColor: "bg-red-500/10",
    },
    {
      icon: Rocket,
      title: "Production Ready",
      description: "Optimized builds, caching strategies, and deployment configurations for production.",
      color: "text-orange-500",
      bgColor: "bg-orange-500/10",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className={cn("py-24 bg-muted/30", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            <span className="gradient-text">Powerful Features</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to build modern, scalable web applications with confidence.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div key={feature.title} variants={itemVariants}>
              <Card className="h-full group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20">
                <CardHeader>
                  <div className="flex items-center space-x-4 mb-4">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      className={cn(
                        "p-3 rounded-lg",
                        feature.bgColor
                      )}
                    >
                      <feature.icon className={cn("w-6 h-6", feature.color)} />
                    </motion.div>
                    <CardTitle className="text-xl group-hover:text-primary transition-colors duration-300">
                      {feature.title}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-16"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-gradient-primary text-white rounded-lg font-semibold glow-effect hover:opacity-90 transition-opacity duration-200"
          >
            Explore All Features
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
