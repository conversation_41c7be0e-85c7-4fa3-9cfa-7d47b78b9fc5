import type { MetaFunction } from "@remix-run/node";
import { Layout } from "~/components/layout";
import { <PERSON> } from "~/components/hero";
import { Features } from "~/components/features";
import { Testimonials } from "~/components/testimonials";

export const meta: MetaFunction = () => {
  return [
    { title: "LuminexClient - Modern Web Applications" },
    {
      name: "description",
      content: "Build stunning, responsive web applications with our modern Remix-based platform. Built with TypeScript, Tailwind CSS, and cutting-edge animations."
    },
    { name: "keywords", content: "remix, react, typescript, tailwind, web development, modern ui" },
    { property: "og:title", content: "LuminexClient - Modern Web Applications" },
    {
      property: "og:description",
      content: "Build stunning, responsive web applications with our modern Remix-based platform."
    },
    { property: "og:type", content: "website" },
  ];
};

export default function Index() {
  return (
    <Layout>
      <Hero />
      <Features />
      <Testimonials />
    </Layout>
  );
}
